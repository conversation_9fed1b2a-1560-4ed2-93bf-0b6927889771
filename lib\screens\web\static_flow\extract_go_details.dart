import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/providers/roles_provider.dart';
import 'package:nsl/providers/go_details_provider.dart';
import 'package:nsl/providers/my_library_provider.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/screens/web/static_flow/customer_onboarding/widgets/custom_dropdown_widget.dart';
import 'package:provider/provider.dart';

/// Helper class for column width constraints
class ColumnConstraints {
  final double minWidth;
  final double maxWidth;

  const ColumnConstraints({
    required this.minWidth,
    required this.maxWidth,
  });
}

class ExtractGoDetailsMiddleStatic extends StatefulWidget {
  final String? sessionId; // New session-based API support
  final String? userIntent;

  const ExtractGoDetailsMiddleStatic({
    super.key,
    this.sessionId,
    this.userIntent,
  });

  @override
  State<ExtractGoDetailsMiddleStatic> createState() =>
      _ExtractGoDetailsMiddleStaticState();
}

class _ExtractGoDetailsMiddleStaticState
    extends State<ExtractGoDetailsMiddleStatic> {
  late AccordionController _accordionController;

  // Simple scroll controller without complex synchronization
  final ScrollController _scrollController = ScrollController();

  // Shared scroll controllers for Excel-like frozen pane behavior
  final ScrollController _verticalController = ScrollController();
  final ScrollController _actionsController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Initialize roles provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final rolesProvider = Provider.of<RolesProvider>(context, listen: false);
      rolesProvider.fetchRoles();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer4<WebHomeProviderStatic, ObjectCreationProvider,
        RolesProvider, GoDetailsProvider>(
      builder: (context, provider, objectCreationProvider, rolesProvider,
          goDetailsProvider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(
                    context, provider, rolesProvider, goDetailsProvider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000), // Black with 10% opacity
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            // provider.isAIMode ? 'Objects' : 'Extracted Details',
            'Extracted Details',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
              fontWeight: FontWeight.bold,
              height: 1,
            ),
          ),

          // Right side with toggle and manually process text
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AI/Manual Toggle
              _buildAIManualToggle(context, provider),

              const SizedBox(width: 16),

              // // Manually Process text
              // Text(
              //   'Manually Process',
              //   style: FontManager.getCustomStyle(
              //     fontSize: ResponsiveFontSizes.titleSmall(context),
              //     color: Colors.white,
              //     fontWeight: FontWeight.w400,
              //     fontFamily: FontManager.fontFamilyTiemposText,
              //     height: 1,
              //   ),
              // ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Toggle switch
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              // Close entity details panel if it's open before switching modes
              final manualProvider =
                  Provider.of<ManualCreationProvider>(context, listen: false);
              if (manualProvider.selectedEntity != null) {
                manualProvider.setSelectedEntity(null);
              }

              provider.toggleAIMode();
              manualProvider.handleEntityValidationForBook();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(width: 4),

        // ai label on the right
        Text(
          'Form',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
        const SizedBox(width: 12),
        // manua; label on the left
        Text(
          'Manual Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: _buildContentWithLineNumbers(
          context, rolesProvider, goDetailsProvider),
    );
  }

  Widget _buildContentWithLineNumbers(BuildContext context,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    int lineNumber = 1;
    final List<Widget> allWidgets = [];

    // Line 1: Solution row
    allWidgets.add(_buildLineWithNumber(lineNumber++,
        _buildFirstRow(context, rolesProvider, goDetailsProvider)));
    allWidgets.add(const SizedBox(height: 16));

    // Line 2: Description row
    allWidgets.add(_buildLineWithNumber(
        lineNumber++, _buildSecondRow(context, goDetailsProvider)));

    // Show local objectives section after validation
    if (goDetailsProvider.currentStep == GoDetailsStep.afterValidation ||
        goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
      allWidgets.add(const SizedBox(height: 24));

      // Line 3: LOCAL OBJECTIVES header
      allWidgets.add(
          _buildLineWithNumber(lineNumber++, _buildLocalObjectivesHeader()));
      allWidgets.add(const SizedBox(height: 8));

      // Local objectives content with line numbers
      if (goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
        // Show LO list with line numbers
        for (final entry in goDetailsProvider.localObjectives.asMap().entries) {
          final index = entry.key;
          final objective = entry.value;

          // Add spacing before each LO (except the first one)
          if (index > 0) {
            allWidgets.add(const SizedBox(height: 12));
          }

          // Add LO item
          allWidgets.add(_buildLineWithNumber(
            lineNumber++,
            _buildLocalObjectiveItem(index, objective, goDetailsProvider),
          ));

          // Add LO insertion text field if open for this LO
          if (goDetailsProvider.isLoInsertionOpen(index)) {
            allWidgets.add(_buildLineWithNumber(
              lineNumber++,
              _buildLoInsertionField(index, goDetailsProvider),
            ));
          }

          // Add pathway creation fields if open for this LO
          if (goDetailsProvider.isPathwayCreationOpen(index)) {
            final selectedType =
                goDetailsProvider.getPathwaySelectedType(index);

            if (selectedType == 'Alternative' || selectedType == 'Parallel') {
              // Add Apply Condition section with individual line numbers
              List<Widget> conditionWidgets =
                  _buildApplyConditionFieldsWithLineNumbers(
                      index, goDetailsProvider, rolesProvider, lineNumber);

              allWidgets.add(
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: IntrinsicWidth(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: conditionWidgets,
                    ),
                  ),
                ),
              );
              lineNumber +=
                  4; // 4 rows: header + first condition + second condition + +LO
            } else if (selectedType == 'Sequential' ||
                selectedType == 'Recursive' ||
                selectedType == 'Terminal') {
              // Add blue container with individual line numbers for Sequential, Recursive, Terminal
              allWidgets.addAll(
                  _buildSequentialRecursiveTerminalFieldsWithLineNumbers(
                      index, goDetailsProvider, rolesProvider, lineNumber));
              lineNumber += 1; // 1 row for these types
            } else {
              // For initial state (no type selected) or any other types - wrap in blue container
              allWidgets.addAll(_buildInitialPathwayFieldsWithLineNumbers(
                  index, goDetailsProvider, rolesProvider, lineNumber));
              lineNumber += 1; // 1 row for initial fields
            }
          }
        }
      } else {
        // Line 4: Input field
        allWidgets.add(_buildLineWithNumber(lineNumber++,
            _buildLocalObjectiveInput(context, goDetailsProvider)));
      }
    }

    return Stack(
      children: [
        // Continuous vertical line
        Positioned(
          left: 28, // Position after line number (20px width + 8px margin)
          top: 0,
          bottom: 0,
          child: Container(
            width: 1,
            color: Colors.grey.shade300,
          ),
        ),
        // Content
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: allWidgets,
        ),
      ],
    );
  }

  // Build Apply Condition fields with individual line numbers outside blue container
  List<Widget> _buildApplyConditionFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);
    final dropdownAttributeWidth = _getAttributeDropdownWidth(context);
    final dropdownConditionWidth = _getConditionDropdownWidth(context);

    // Row 1: Apply Condition header with line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7), // Light blue background
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
        ),
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 16),
            const Text(
              'Apply Condition',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w700,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ],
        ),
      ),
    ));

    // Row 2: First condition row with pathway fields and line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7), // Light blue background
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: [
            // Select Role dropdown
            SizedBox(
              width: dropdownWidth,
              child: _buildPathwayRoleField(
                  loIndex, goDetailsProvider, rolesProvider),
            ),
            const SizedBox(width: 8),

            // Select Type dropdown
            SizedBox(
              width: dropdownWidth,
              child: _buildPathwayTypeField(loIndex, goDetailsProvider),
            ),
            const SizedBox(width: 8),

            // Select LO dropdown
            SizedBox(
              width: dropdownWidth,
              child: _buildPathwayLOField(loIndex, goDetailsProvider),
            ),
            const SizedBox(width: 16),

            // Entity Attribute dropdown
            SizedBox(
              width: dropdownAttributeWidth,
              child: _buildEntityAttributeDropdown(),
            ),
            const SizedBox(width: 8),

            // Condition dropdown
            SizedBox(
              width: dropdownWidth,
              child: _buildConditionDropdown(),
            ),
            const SizedBox(width: 8),

            // Entity Attribute dropdown (second)
            SizedBox(
              width: dropdownAttributeWidth,
              child: _buildEntityAttributeDropdown(),
            ),
          ],
        ),
      ),
    ));

    // Row 3: Second condition row with line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7), // Light blue background
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: [
            // Empty space to align with dropdowns above
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),

            // Second LO dropdown
            SizedBox(
              width: dropdownWidth,
              child: _buildPathwayLOField(loIndex, goDetailsProvider),
            ),
            const SizedBox(width: 16),

            // Entity Attribute dropdown
            SizedBox(
              width: dropdownAttributeWidth,
              child: _buildEntityAttributeDropdown(),
            ),
            const SizedBox(width: 8),

            // Condition dropdown
            SizedBox(
              width: dropdownWidth,
              child: _buildConditionDropdown(),
            ),
            const SizedBox(width: 8),

            // Entity Attribute dropdown (second)
            SizedBox(
              width: dropdownAttributeWidth,
              child: _buildEntityAttributeDropdown(),
            ),
          ],
        ),
      ),
    ));

    // Row 4: + LO button with line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7), // Light blue background
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(4),
            bottomRight: Radius.circular(4),
          ),
        ),
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),
            const Text(
              '+ LO',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Colors.blue,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ],
        ),
      ),
    ));

    return widgets;
  }

  // Build initial pathway fields (Role and Type dropdowns) with blue container and line numbers
  List<Widget> _buildInitialPathwayFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);

    // Single row with blue background container and line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color:
              Color(0xFFF7F7F7), // Blue background (same as other containers)
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
        padding: const EdgeInsets.all(12),
        child: _buildInitialPathwayContent(
            loIndex, goDetailsProvider, rolesProvider, dropdownWidth),
      ),
    ));

    return widgets;
  }

  // Build content for initial pathway creation (Role and Type dropdowns only)
  Widget _buildInitialPathwayContent(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      double dropdownWidth) {
    return Row(
      children: [
        // Select Role dropdown
        SizedBox(
          width: dropdownWidth,
          child:
              _buildPathwayRoleField(loIndex, goDetailsProvider, rolesProvider),
        ),
        const SizedBox(width: 8),

        // Select Type dropdown
        SizedBox(
          width: dropdownWidth,
          child: _buildPathwayTypeField(loIndex, goDetailsProvider),
        ),
        // No third field for initial state
      ],
    );
  }

  // Build Sequential, Recursive, Terminal fields with individual line numbers and blue container
  List<Widget> _buildSequentialRecursiveTerminalFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);

    // Single row with blue background container and line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7),
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
        padding: const EdgeInsets.all(12),
        child: _buildSequentialRecursiveTerminalContent(
            loIndex, goDetailsProvider, rolesProvider, dropdownWidth),
      ),
    ));

    return widgets;
  }

  // Build content for Sequential, Recursive, Terminal types
  Widget _buildSequentialRecursiveTerminalContent(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      double dropdownWidth) {
    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);

    return Row(
      children: [
        // Select Role dropdown
        SizedBox(
          width: dropdownWidth,
          child:
              _buildPathwayRoleField(loIndex, goDetailsProvider, rolesProvider),
        ),
        const SizedBox(width: 8),

        // Select Type dropdown
        SizedBox(
          width: dropdownWidth,
          child: _buildPathwayTypeField(loIndex, goDetailsProvider),
        ),

        // Third field based on selected type
        if (selectedType == 'Sequential') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayLOField(loIndex, goDetailsProvider),
          ),
        ] else if (selectedType == 'Recursive') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: _buildRecursiveInputField(loIndex, goDetailsProvider),
          ),
        ],
        // Terminal type has no third field
      ],
    );
  }

  Widget _buildFirstRow(BuildContext context, RolesProvider rolesProvider,
      GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        // Solution label and text field
        Expanded(
          flex: 3,
          child: Row(
            children: [
              const Text(
                'Solution:',
                style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
              ),
              const SizedBox(width: AppSpacing.xs),
              Expanded(
                child: Container(
                  height: 24,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: TextField(
                    controller: goDetailsProvider.solutionController,
                    decoration: const InputDecoration(
                      hintText: 'Type here..',
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      fillColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: 4, vertical: 7), // Remove default padding
                      isDense: true,
                      //  contentPadding: EdgeInsets.symmetric(horizontal: 12,),
                      // hintText: 'Enter solution name',
                    ),
                    style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                        fontFamily: FontManager.fontFamilyTiemposText),
                  ),
                ),
              ),
              const SizedBox(width: AppSpacing.xs),
              // Tick icon button
              InkWell(
                onTap: goDetailsProvider.isValidating
                    ? null
                    : () {
                        goDetailsProvider.validateSolution();
                      },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: const Color(0xFF007AFF),
                    shape: BoxShape.circle,
                  ),
                  child:
                      //  goDetailsProvider.isValidating
                      //     ? const SizedBox(
                      //         width: 16,
                      //         height: 16,
                      //         child: CircularProgressIndicator(
                      //           strokeWidth: 2,
                      //           valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      //         ),
                      //       )
                      //     :
                      const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(width: 24),

        // Agent Type dropdown
        Expanded(
          flex: 1,
          child: Consumer<MyLibraryProvider>(
            builder: (context, libraryProvider, child) {
              return _buildAgentTypeDropdown(
                  context, libraryProvider, goDetailsProvider);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAgentTypeDropdown(BuildContext context,
      MyLibraryProvider libraryProvider, GoDetailsProvider goDetailsProvider) {

    // Fetch library data if not already loaded
    if (!libraryProvider.isLoading && libraryProvider.roles.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        libraryProvider.fetchLibraryData();
      });
    }

    return Row(
      children: [
        Expanded(
          child: SizedBox(
            height: 24,
            child: CustomDropdownWidget(
              label: 'Role Type',
              list: libraryProvider.isLoading
                  ? ['Loading roles...']
                  : libraryProvider.roles.isEmpty
                      ? ['No roles available']
                      : libraryProvider.getRoleNames(),
              value: libraryProvider.selectedRole?.name,
              onChanged: (value) {
                if (!libraryProvider.isLoading &&
                    value != null &&
                    value != 'Loading roles...' &&
                    value != 'No roles available') {
                  libraryProvider.setSelectedRoleByName(value);

                  // Convert RolesPostgre to PostgresRole for compatibility with GoDetailsProvider
                  final selectedLibraryRole = libraryProvider.getRoleByName(value);
                  if (selectedLibraryRole != null) {
                    // Create a PostgresRole object from RolesPostgre
                    final postgresRole = PostgresRole(
                      roleId: selectedLibraryRole.roleId,
                      name: selectedLibraryRole.name,
                      description: selectedLibraryRole.description,
                      tenantId: null, // Skip tenantId due to type mismatch
                      createdAt: selectedLibraryRole.createdAt,
                      updatedAt: selectedLibraryRole.updatedAt,
                    );
                    goDetailsProvider.setSelectedRole(postgresRole);
                  }
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSecondRow(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        const Text(
          'Description:',
          style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w700,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText),
        ),
        const SizedBox(width: AppSpacing.xs),
        Expanded(
          child: Container(
            height: 24,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
            ),
            child: TextField(
              controller: goDetailsProvider.descriptionController,
              decoration: const InputDecoration(
                border: InputBorder.none,
                isDense: true,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding: EdgeInsets.symmetric(
                    horizontal: 4, vertical: 7), // Remove default padding

                hintText: 'Enter description..',
              ),
              style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLocalObjectivesSection(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // LOCAL OBJECTIVES header
        const Row(
          children: [
            Text(
              'LOCAL OBJECTIVES',
              style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.xs),
        // Local objectives content - show input or list based on step
        if (goDetailsProvider.currentStep ==
            GoDetailsStep.afterLocalObjectives) ...[
          // Show LO list
          _buildLocalObjectivesList(context, goDetailsProvider),
        ] else ...[
          // Show input field
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 24,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: TextField(
                    controller: goDetailsProvider.localObjectiveController,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      isDense: true,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      fillColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 4, vertical: 7),
                      hintText: 'Type LO name with full stop (.)',
                      hintStyle: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText),
                    ),
                    style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                        fontFamily: FontManager.fontFamilyTiemposText),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Tick icon for local objective
              InkWell(
                onTap: () {
                  goDetailsProvider.processLocalObjectives();
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: const Color(0xFF007AFF),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildLocalObjectivesList(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Build LO items
        ...goDetailsProvider.localObjectives.asMap().entries.map((entry) {
          final index = entry.key;
          final objective = entry.value;

          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Row(
              children: [
                // LO number and text
                Expanded(
                  child: Text(
                    'LO-${index + 1}. $objective',
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Create pathway button
                InkWell(
                  onTap: () {
                    // Handle create pathway for this LO
                    print(
                        'Create pathway clicked for LO-${index + 1}: $objective');
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      'Create pathway',
                      style: TextStyle(
                        fontSize: 8,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  // Helper methods for line numbers
  Widget _buildLineWithNumber(int lineNumber, Widget content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Line number
        Container(
          // color: Color(0xFFEDF3FF),
          width: 20,
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
        ),
        // Space for the continuous vertical line
        const SizedBox(width: 17), // 8px margin + 1px line + 8px margin
        // Content
        Expanded(child: content),
      ],
    );
  }

  Widget _buildLocalObjectivesHeader() {
    return const Text(
      'LOCAL OBJECTIVES',
      style: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w700,
        color: Colors.black,
        fontFamily: FontManager.fontFamilyTiemposText,
      ),
    );
  }

  Widget _buildLocalObjectiveItem(
      int index, String objective, GoDetailsProvider goDetailsProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // LO number and text
        Text(
          'LO-${index + 1}. $objective',
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.black,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        // Create pathway button
        InkWell(
          onTap: () {
            goDetailsProvider.togglePathwayCreation(index);
          },
          child: const Text(
            'Create pathway',
            style: TextStyle(
              decoration: TextDecoration.underline,
              decorationColor: Colors.blue,
              fontSize: 10,
              fontWeight: FontWeight.w500,
              height: 2.0,
              color: Colors.blue,
            ),
          ),
        ),

        Spacer(),
        InkWell(
          onTap: () {
            goDetailsProvider.toggleLoInsertion(index);
          },
          child: const Icon(
            Icons.add,
            color: Colors.black,
            size: 18,
          ),
        ),
      ],
    );
  }

  Widget _buildLocalObjectiveInput(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 24,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
            ),
            child: TextField(
              controller: goDetailsProvider.localObjectiveController,
              decoration: const InputDecoration(
                border: InputBorder.none,
                isDense: true,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 4, vertical: 7),
                hintText: 'Type LO name with full stop (.)',
                hintStyle: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
              ),
              style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText),
            ),
          ),
        ),
        const SizedBox(width: 8),
        // Tick icon for local objective
        InkWell(
          onTap: () {
            goDetailsProvider.processLocalObjectives();
          },
          child: Container(
            width: 24,
            height: 24,
            decoration: const BoxDecoration(
              color: Color(0xFF007AFF),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoInsertionField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final controller = goDetailsProvider.getLoInsertionController(loIndex);
    if (controller == null) return const SizedBox.shrink();

    return Column(
      children: [
        SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              child: Container(
                height: 24,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: TextField(
                  controller: controller,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    fillColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4, vertical: 7),
                    hintText: 'Type LO names with full stop (.)',
                    hintStyle: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey,
                        fontFamily: FontManager.fontFamilyTiemposText),
                  ),
                  style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText),
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Add icon for inserting LOs
            InkWell(
              onTap: () {
                goDetailsProvider.processLoInsertion(loIndex);
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Color(0xFF007AFF),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 14,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper method to get responsive dropdown width
  double _getDropdownWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth <= 1366) {
      return 100.0;
    } else {
      return (screenWidth * 100) / 1366; // Scale proportionally
    }
  }

  double _getAttributeDropdownWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth <= 1366) {
      return 160.0;
    } else {
      return (screenWidth * 160) / 1366; // Scale proportionally
    }
  }

  double _getConditionDropdownWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth <= 1366) {
      return 57.0;
    } else {
      return (screenWidth * 57) / 1366; // Scale proportionally
    }
  }

  // Pathway creation helper methods
  List<Widget> _buildPathwayCreationFields(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;

    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);

    if (selectedType == 'Alternative' || selectedType == 'Parallel') {
      // Create a container with blue background that contains multiple rows with line numbers
      widgets.add(_buildApplyConditionSectionWithLineNumbers(
          loIndex, goDetailsProvider, rolesProvider, currentLineNumber));
    } else if (selectedType == 'Sequential' ||
        selectedType == 'Recursive' ||
        selectedType == 'Terminal') {
      // Add blue container with individual line numbers for Sequential, Recursive, Terminal
      widgets.addAll(_buildSequentialRecursiveTerminalFieldsWithLineNumbers(
          loIndex, goDetailsProvider, rolesProvider, currentLineNumber));
    } else {
      // For initial state (no type selected) or any other types - wrap in blue container
      widgets.addAll(_buildInitialPathwayFieldsWithLineNumbers(
          loIndex, goDetailsProvider, rolesProvider, currentLineNumber));
    }

    return widgets;
  }

  // Build Apply Condition section with individual line numbers for each row
  Widget _buildApplyConditionSectionWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);
    final dropdownAttributeWidth = _getAttributeDropdownWidth(context);
    final dropdownConditionWidth = _getConditionDropdownWidth(context);
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: const Color(0xFFEDF3FF), // Light blue background
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row 1: Apply Condition header with line number
          _buildLineWithNumber(
            currentLineNumber++,
            Row(
              children: [
                SizedBox(width: dropdownWidth),
                const SizedBox(width: 8),
                SizedBox(width: dropdownWidth),
                const SizedBox(width: 8),
                SizedBox(width: dropdownWidth),
                const SizedBox(width: 16),
                const Text(
                  'Apply Condition',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          // Row 2: First condition row with pathway fields and line number
          _buildLineWithNumber(
            currentLineNumber++,
            Row(
              children: [
                // Select Role dropdown
                SizedBox(
                  width: dropdownWidth,
                  child: _buildPathwayRoleField(
                      loIndex, goDetailsProvider, rolesProvider),
                ),
                const SizedBox(width: 8),

                // Select Type dropdown
                SizedBox(
                  width: dropdownWidth,
                  child: _buildPathwayTypeField(loIndex, goDetailsProvider),
                ),
                const SizedBox(width: 8),

                // Select LO dropdown
                SizedBox(
                  width: dropdownWidth,
                  child: _buildPathwayLOField(loIndex, goDetailsProvider),
                ),
                const SizedBox(width: 16),

                // Entity Attribute dropdown
                SizedBox(
                  width: dropdownAttributeWidth,
                  child: _buildEntityAttributeDropdown(),
                ),
                const SizedBox(width: 8),

                // Condition dropdown
                SizedBox(
                  width: dropdownWidth,
                  child: _buildConditionDropdown(),
                ),
                const SizedBox(width: 8),

                // Entity Attribute dropdown (second)
                SizedBox(
                  width: dropdownAttributeWidth,
                  child: _buildEntityAttributeDropdown(),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          // Row 3: Second condition row with line number
          _buildLineWithNumber(
            currentLineNumber++,
            Row(
              children: [
                // Empty space to align with dropdowns above
                SizedBox(width: dropdownWidth),
                const SizedBox(width: 8),
                SizedBox(width: dropdownWidth),
                const SizedBox(width: 8),

                // Second LO dropdown
                SizedBox(
                  width: dropdownWidth,
                  child: _buildPathwayLOField(loIndex, goDetailsProvider),
                ),
                const SizedBox(width: 16),

                // Entity Attribute dropdown
                SizedBox(
                  width: dropdownAttributeWidth,
                  child: _buildEntityAttributeDropdown(),
                ),
                const SizedBox(width: 8),

                // Condition dropdown
                SizedBox(
                  width: dropdownWidth,
                  child: _buildConditionDropdown(),
                ),
                const SizedBox(width: 8),

                // Entity Attribute dropdown (second)
                SizedBox(
                  width: dropdownAttributeWidth,
                  child: _buildEntityAttributeDropdown(),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          // Row 4: + LO button with line number
          _buildLineWithNumber(
            currentLineNumber++,
            Row(
              children: [
                SizedBox(width: dropdownWidth),
                const SizedBox(width: 8),
                SizedBox(width: dropdownWidth),
                const SizedBox(width: 8),
                const Text(
                  '+ LO',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.blue,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build the entire Apply Condition section in one container
  Widget _buildApplyConditionSection(int loIndex,
      GoDetailsProvider goDetailsProvider, RolesProvider rolesProvider) {
    final dropdownWidth = _getDropdownWidth(context);

    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: const Color(0xFFEDF3FF), // Light blue background
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Apply Condition header
          Row(
            children: [
              SizedBox(width: dropdownWidth),
              const SizedBox(width: 8),
              SizedBox(width: dropdownWidth),
              const SizedBox(width: 8),
              SizedBox(width: dropdownWidth),
              const SizedBox(width: 16),
              const Text(
                'Apply Condition',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // First condition row with pathway fields
          _buildConditionRowContent(
              loIndex, goDetailsProvider, rolesProvider, 0),
          const SizedBox(height: 8),

          // Second condition row
          _buildConditionRowContent(
              loIndex, goDetailsProvider, rolesProvider, 1),
          const SizedBox(height: 8),

          // + LO button row
          Row(
            children: [
              SizedBox(width: dropdownWidth),
              const SizedBox(width: 8),
              SizedBox(width: dropdownWidth),
              const SizedBox(width: 8),
              const Text(
                '+ LO',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build condition row content
  Widget _buildConditionRowContent(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int rowIndex) {
    final dropdownWidth = _getDropdownWidth(context);
    final dropdownAttributeWidth = _getAttributeDropdownWidth(context);
    final dropdownConditionWidth = _getConditionDropdownWidth(context);
    if (rowIndex == 0) {
      // First row: pathway fields + condition fields
      return Row(
        children: [
          // Select Role dropdown
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayRoleField(
                loIndex, goDetailsProvider, rolesProvider),
          ),
          const SizedBox(width: 8),

          // Select Type dropdown
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayTypeField(loIndex, goDetailsProvider),
          ),
          const SizedBox(width: 8),

          // Select LO dropdown
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayLOField(loIndex, goDetailsProvider),
          ),
          const SizedBox(width: 16),

          // Entity Attribute dropdown
          SizedBox(
            width: dropdownAttributeWidth,
            child: _buildEntityAttributeDropdown(),
          ),
          const SizedBox(width: 8),

          // Condition dropdown
          SizedBox(
            width: dropdownConditionWidth,
            child: _buildConditionDropdown(),
          ),
          const SizedBox(width: 8),

          // Entity Attribute dropdown (second)
          SizedBox(
            width: dropdownAttributeWidth,
            child: _buildEntityAttributeDropdown(),
          ),
        ],
      );
    } else {
      // Second row: empty space + second LO + condition fields
      return Row(
        children: [
          // Empty space to align with dropdowns above
          SizedBox(width: dropdownWidth),
          const SizedBox(width: 8),
          SizedBox(width: dropdownWidth),
          const SizedBox(width: 8),

          // Second LO dropdown
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayLOField(loIndex, goDetailsProvider),
          ),
          const SizedBox(width: 16),

          // Entity Attribute dropdown
          SizedBox(
            width: dropdownAttributeWidth,
            child: _buildEntityAttributeDropdown(),
          ),
          const SizedBox(width: 8),

          // Condition dropdown
          SizedBox(
            width: dropdownConditionWidth,
            child: _buildConditionDropdown(),
          ),
          const SizedBox(width: 8),

          // Entity Attribute dropdown (second)
          SizedBox(
            width: dropdownAttributeWidth,
            child: _buildEntityAttributeDropdown(),
          ),
        ],
      );
    }
  }

  int _getPathwayFieldsCount(int loIndex, GoDetailsProvider goDetailsProvider) {
    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);
    if (selectedType == 'Alternative' || selectedType == 'Parallel') {
      return 4; // Apply Condition header + First condition row + Second condition row + +LO row
    }
    return 1; // Just the main row for Sequential, Recursive, Terminal
  }

  // Build Apply Condition header row only
  Widget _buildApplyConditionHeaderRow(BuildContext context) {
    final dropdownWidth = _getDropdownWidth(context);

    return Row(
      children: [
        // Empty space to align with dropdowns above
        SizedBox(width: dropdownWidth),
        const SizedBox(width: 8),
        SizedBox(width: dropdownWidth),
        const SizedBox(width: 8),
        SizedBox(width: dropdownWidth),
        const SizedBox(width: 16),

        // Apply Condition header only
        Column(
          children: [
            const Text(
              'Apply Condition',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w700,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ],
    );
  }

  // Build main row with pathway fields + first condition row
  Widget _buildMainRowWithCondition(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int conditionRowIndex,
      BuildContext context) {
    final dropdownWidth = _getDropdownWidth(context);

    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: const Color(0xFFEDF3FF), // Light blue background
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          // Select Role dropdown
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayRoleField(
                loIndex, goDetailsProvider, rolesProvider),
          ),
          const SizedBox(width: 8),

          // Select Type dropdown
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayTypeField(loIndex, goDetailsProvider),
          ),
          const SizedBox(width: 8),

          // Select LO dropdown
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayLOField(loIndex, goDetailsProvider),
          ),
          const SizedBox(width: 16),

          // Entity Attribute dropdown
          SizedBox(
            width: dropdownWidth,
            child: _buildEntityAttributeDropdown(),
          ),
          const SizedBox(width: 8),

          // Condition dropdown
          SizedBox(
            width: dropdownWidth,
            child: _buildConditionDropdown(),
          ),
          const SizedBox(width: 8),

          // Entity Attribute dropdown (second)
          SizedBox(
            width: dropdownWidth,
            child: _buildEntityAttributeDropdown(),
          ),
        ],
      ),
    );
  }

  // Build second row with empty space + second condition row
  Widget _buildSecondRowWithCondition(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      int conditionRowIndex,
      BuildContext context) {
    final dropdownWidth = _getDropdownWidth(context);
    final dropdownAttributeWidth = _getAttributeDropdownWidth(context);

    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: const Color(0xFFEDF3FF), // Light blue background
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          // Empty space to align with dropdowns above
          SizedBox(width: dropdownWidth),
          const SizedBox(width: 8),
          SizedBox(width: dropdownWidth),
          const SizedBox(width: 8),
          // second Lo Dropdown
          Column(
            children: [
              const SizedBox(height: 10),
              SizedBox(
                width: dropdownWidth,
                child: _buildPathwayLOField(loIndex, goDetailsProvider),
              ),
            ],
          ),
          const SizedBox(width: 16),
          // Entity Attribute dropdown
          Column(
            children: [
              const SizedBox(height: 10),
              SizedBox(
                width: dropdownAttributeWidth,
                child: _buildEntityAttributeDropdown(),
              ),
            ],
          ),
          const SizedBox(width: 8),

          // Condition dropdown
          Column(
            children: [
              const SizedBox(height: 10),
              SizedBox(
                width: dropdownWidth,
                child: _buildConditionDropdown(),
              ),
            ],
          ),
          const SizedBox(width: 8),

          // Entity Attribute dropdown (second)
          Column(
            children: [
              const SizedBox(height: 10),
              SizedBox(
                width: dropdownAttributeWidth,
                child: _buildEntityAttributeDropdown(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPathwayFieldsRow(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      BuildContext context) {
    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);
    final dropdownWidth = _getDropdownWidth(context);

    return Row(
      children: [
        // Select Role dropdown
        SizedBox(
          width: dropdownWidth,
          child:
              _buildPathwayRoleField(loIndex, goDetailsProvider, rolesProvider),
        ),
        const SizedBox(width: 8),

        // Select Type dropdown
        SizedBox(
          width: dropdownWidth,
          child: _buildPathwayTypeField(loIndex, goDetailsProvider),
        ),

        // Third field based on selected type
        if (selectedType == 'Sequential') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayLOField(loIndex, goDetailsProvider),
          ),
        ] else if (selectedType == 'Alternative' ||
            selectedType == 'Parallel') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayLOField(loIndex, goDetailsProvider),
          ),
        ] else if (selectedType == 'Recursive') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: _buildRecursiveInputField(loIndex, goDetailsProvider),
          ),
        ],
        // Terminal type has no third field
      ],
    );
  }

  // Helper method for Entity Attribute dropdown
  Widget _buildEntityAttributeDropdown() {
    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Entity Attribute',
        list: const [], // Add your entity attributes here
        value: null,
        onChanged: (value) {
          // Handle entity attribute selection
        },
      ),
    );
  }

  // Helper method for Condition dropdown
  Widget _buildConditionDropdown() {
    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Condition',
        list: const [], // Add your conditions here
        value: null,
        onChanged: (value) {
          // Handle condition selection
        },
      ),
    );
  }

  Widget _buildPathwayRoleField(int loIndex,
      GoDetailsProvider goDetailsProvider, RolesProvider rolesProvider) {
    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select Role',
        list: rolesProvider.isLoading
            ? ['Loading roles...']
            : rolesProvider.roles.isEmpty
                ? ['No roles available']
                : rolesProvider.roles
                    .map((role) => role.name ?? 'Unknown Role')
                    .toList(),
        value: goDetailsProvider.getPathwaySelectedRole(loIndex)?.name,
        onChanged: (value) {
          if (!rolesProvider.isLoading &&
              value != null &&
              value != 'Loading roles...' &&
              value != 'No roles available') {
            final selectedRole = rolesProvider.roles.firstWhere(
              (role) => role.name == value,
              orElse: () => rolesProvider.roles.first,
            );
            goDetailsProvider.setPathwaySelectedRole(loIndex, selectedRole);
          }
        },
      ),
    );
  }

  Widget _buildPathwayTypeField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final typeOptions = [
      'Sequential',
      'Alternative',
      'Parallel',
      'Recursive',
      'Terminal'
    ];

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select Type',
        list: typeOptions,
        value: goDetailsProvider.getPathwaySelectedType(loIndex),
        onChanged: (selectedType) {
          goDetailsProvider.setPathwaySelectedType(loIndex, selectedType);
        },
      ),
    );
  }

  Widget _buildPathwayLOField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final availableLOs =
        goDetailsProvider.getAvailableLOsForSequential(loIndex);

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select LO',
        list: availableLOs.isEmpty ? ['No LOs available'] : availableLOs,
        value: goDetailsProvider.getPathwaySelectedLO(loIndex),
        onChanged: (selectedLO) {
          if (availableLOs.isNotEmpty && selectedLO != 'No LOs available') {
            goDetailsProvider.setPathwaySelectedLO(loIndex, selectedLO);
          }
        },
      ),
    );
  }

  // Additional pathway creation helper methods
  Widget _buildRecursiveInputField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    return Container(
      height: 24,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(2),
      ),
      child: TextField(
        decoration: const InputDecoration(
          border: InputBorder.none,
          isDense: true,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          fillColor: Colors.transparent,
          hoverColor: Colors.transparent,
          contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 7),
          hintText: 'Input number',
          hintStyle: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        style: const TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: Colors.black,
          fontFamily: FontManager.fontFamilyTiemposText,
        ),
      ),
    );
  }

  Widget _buildAddLOButton(BuildContext context) {
    final dropdownWidth = _getDropdownWidth(context);

    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: const Color(0xFFEDF3FF), // Light blue background
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          SizedBox(width: dropdownWidth),
          const SizedBox(width: 8),
          SizedBox(width: dropdownWidth),
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: const Text(
              '+ LO',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Colors.blue,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
